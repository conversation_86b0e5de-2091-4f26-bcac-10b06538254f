import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AdminUserActions } from './AdminUserActions';
import { AdminUsers } from './AdminUsers';
import { AdvanclyLoans } from './AdvanclyLoans';
import { AppealRequests } from './AppealRequests';
import { ApproverDepartment } from './ApproverDepartment';
import { AssignableAdmins } from './AssignableAdmins';
import { AutoLimitJobs } from './AutoLimitJobs';
import { BankTransfers } from './BankTransfers';
import { BaxiLogs } from './BaxiLogs';
import { BillTransactions } from './BillTransactions';
import { CashWalletTransferAwaitingApprovals } from './CashWalletTransferAwaitingApprovals';
import { Comments } from './Comments';
import { CommunicationTemplates } from './CommunicationTemplates';
import { Communications } from './Communications';
import { CompanyAccounts } from './CompanyAccounts';
import { CompanyCards } from './CompanyCards';
import { CompanyCustomPlanRequests } from './CompanyCustomPlanRequests';
import { CompanyPlanUpgradeRequests } from './CompanyPlanUpgradeRequests';
import { CreditCardActivities } from './CreditCardActivities';
import { CreditCardPayments } from './CreditCardPayments';
import { CreditCardPendingRepayments } from './CreditCardPendingRepayments';
import { CreditCardRepaymentReceipts } from './CreditCardRepaymentReceipts';
import { CreditCardWalletHistories } from './CreditCardWalletHistories';
import { CustomNotifications } from './CustomNotifications';
import { Devices } from './Devices';
import { Documents } from './Documents';
import { EquityContributions } from './EquityContributions';
import { ExpenseApprovers } from './ExpenseApprovers';
import { ExpensePolicyUser } from './ExpensePolicyUser';
import { ExpenseRequests } from './ExpenseRequests';
import { ExpressVerifications } from './ExpressVerifications';
import { FailedPayments } from './FailedPayments';
import { FeatureNotificationSubscriptions } from './FeatureNotificationSubscriptions';
import { FireBaseDeviceTokens } from './FireBaseDeviceTokens';
import { FlutterwaveLogs } from './FlutterwaveLogs';
import { IncreaseLoanLimits } from './IncreaseLoanLimits';
import { InvalidateDocuments } from './InvalidateDocuments';
import { LenderCreditCardLoans } from './LenderCreditCardLoans';
import { LoanbotBlankStatements } from './LoanbotBlankStatements';
import { LoanbotStatementAnalyses } from './LoanbotStatementAnalyses';
import { LoanbotVerifications } from './LoanbotVerifications';
import { Loans } from './Loans';
import { MerchantAnonymousUsers } from './MerchantAnonymousUsers';
import { MerchantOrders } from './MerchantOrders';
import { MerchantUser } from './MerchantUser';
import { MyBankStatements } from './MyBankStatements';
import { OtpVerifications } from './OtpVerifications';
import { Payments } from './Payments';
import { PaystackTransferRecipients } from './PaystackTransferRecipients';
import { PersonalAccountStatements } from './PersonalAccountStatements';
import { PersonalCardAccounts } from './PersonalCardAccounts';
import { PersonalCardReconciliations } from './PersonalCardReconciliations';
import { PersonalCardTransactions } from './PersonalCardTransactions';
import { PersonalCardUtilizations } from './PersonalCardUtilizations';
import { PersonalClearedStatements } from './PersonalClearedStatements';
import { PersonalCreditApplications } from './PersonalCreditApplications';
import { PersonalCreditCardRepayments } from './PersonalCreditCardRepayments';
import { PersonalCredits } from './PersonalCredits';
import { PersonalDeferredPlanPayments } from './PersonalDeferredPlanPayments';
import { PersonalLoanOfferLetters } from './PersonalLoanOfferLetters';
import { PersonalLoanRepaymentDefaultCharges } from './PersonalLoanRepaymentDefaultCharges';
import { PersonalRepaymentCards } from './PersonalRepaymentCards';
import { PersonalRepaymentTransactions } from './PersonalRepaymentTransactions';
import { RemitaLogs } from './RemitaLogs';
import { RepaymentWalletHistories } from './RepaymentWalletHistories';
import { RepaymentWalletTransactions } from './RepaymentWalletTransactions';
import { Repayments } from './Repayments';
import { RequeuedUsers } from './RequeuedUsers';
import { SecureCards } from './SecureCards';
import { SecuredInvestments } from './SecuredInvestments';
import { StatementDocuments } from './StatementDocuments';
import { TangarineTasks } from './TangarineTasks';
import { TransactionPins } from './TransactionPins';
import { TransferCollections } from './TransferCollections';
import { Transfers } from './Transfers';
import { Trips } from './Trips';
import { UsedTransactionPins } from './UsedTransactionPins';
import { UserBankStatements } from './UserBankStatements';
import { UserPlans } from './UserPlans';
import { UserProfiles } from './UserProfiles';
import { UserReferrals } from './UserReferrals';
import { UserSecurityQuestions } from './UserSecurityQuestions';
import { UserStatementComments } from './UserStatementComments';
import { Companies } from './Companies';
import { Groups } from './Groups';
import { Merchants } from './Merchants';
import { Units } from './Units';
import { Permissions } from './Permissions';
import { Roles } from './Roles';
import { VerifyDeclinedPayments } from './VerifyDeclinedPayments';
import { VirtualAccounts } from './VirtualAccounts';
import { WalletLimitRequests } from './WalletLimitRequests';
import { Watchlists } from './Watchlists';
import { WorkplaceVerifications } from './WorkplaceVerifications';
import { SelfieAttempt } from './SelfieAttempt';

@Index('users_agent_code_index', ['agentCode'], {})
@Index('users_auth_token_unique', ['authToken'], { unique: true })
@Index('users_bolt_user_id_index', ['boltUserId'], {})
@Index('users_bolt_user_status_index', ['boltUserStatus'], {})
@Index('users_bvn_verified_at_index', ['bvnVerifiedAt'], {})
@Index('users_company_id_foreign', ['companyId'], {})
@Index('users_created_at_index', ['createdAt'], {})
@Index('users_email_unique', ['email'], { unique: true })
@Index('users_group_id_foreign', ['groupId'], {})
@Index('users_in_tangarine_at_index', ['inTangarineAt'], {})
@Index('users_last_name_index', ['lastName'], {})
@Index('users_loanbot_status_index', ['loanbotStatus'], {})
@Index('users_merchant_id_foreign', ['merchantId'], {})
@Index('users_name_index', ['name'], {})
@Index('users_nin_verified_at_index', ['ninVerifiedAt'], {})
@Index('users_official_email_index', ['officialEmail'], {})
@Index('users_phone_no_unique', ['phoneNo'], { unique: true })
@Index('users_source_index', ['source'], {})
@Index('users_status_index', ['status'], {})
@Index('users_unit_id_foreign', ['unitId'], {})
@Index('users_work_email_verified_at_index', ['workEmailVerifiedAt'], {})
@Entity('users')
export class Users {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', { name: 'company_id', nullable: true, unsigned: true })
  companyId: string | null;

  @Column('char', { name: 'merchant_id', nullable: true, length: 36 })
  merchantId: string | null;

  @Column('bigint', { name: 'group_id', nullable: true, unsigned: true })
  groupId: string | null;

  @Column('bigint', { name: 'unit_id', nullable: true, unsigned: true })
  unitId: string | null;

  @Column('varchar', { name: 'name', length: 255 })
  name: string;

  @Column('varchar', { name: 'last_name', nullable: true, length: 255 })
  lastName: string | null;

  @Column('varchar', { name: 'gender', nullable: true, length: 255 })
  gender: string | null;

  @Column('varchar', { name: 'official_email', nullable: true, length: 255 })
  officialEmail: string | null;

  @Column('varchar', { name: 'email', unique: true, length: 255 })
  email: string;

  @Column('timestamp', { name: 'email_verified_at', nullable: true })
  emailVerifiedAt: Date | null;

  @Column('varchar', { name: 'phone_no', unique: true, length: 255 })
  phoneNo: string;

  @Column('datetime', { name: 'mobile_verified_at', nullable: true })
  mobileVerifiedAt: Date | null;

  @Column('timestamp', { name: 'logged_in_at', nullable: true })
  loggedInAt: Date | null;

  @Column('datetime', { name: 'logged_out_at', nullable: true })
  loggedOutAt: Date | null;

  @Column('varchar', { name: 'password', length: 255 })
  password: string;

  @Column('varchar', {
    name: 'source',
    nullable: true,
    length: 250,
    default: () => "'web'",
  })
  source: string | null;

  @Column('varchar', {
    name: 'status',
    nullable: true,
    length: 255,
    default: () => "'incomplete-0'",
  })
  status: string | null;

  @Column('int', { name: 'bolt_user_id', nullable: true })
  boltUserId: number | null;

  @Column('varchar', { name: 'bolt_user_status', nullable: true, length: 32 })
  boltUserStatus: string | null;

  @Column('datetime', { name: 'bolt_opted_in_at', nullable: true })
  boltOptedInAt: Date | null;

  @Column('varchar', { name: 'agent_code', nullable: true, length: 255 })
  agentCode: string | null;

  @Column('timestamp', { name: 'change_password_at', nullable: true })
  changePasswordAt: Date | null;

  @Column('varchar', { name: 'remember_token', nullable: true, length: 100 })
  rememberToken: string | null;

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null;

  @Column('timestamp', { name: 'updated_at', nullable: true })
  updatedAt: Date | null;

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null;

  @Column('varchar', { name: 'credpal_id', nullable: true, length: 255 })
  credpalId: string | null;

  @Column('datetime', { name: 'in_tangarine_at', nullable: true })
  inTangarineAt: Date | null;

  @Column('tinyint', { name: 'can_have_debit', width: 1, default: () => "'0'" })
  canHaveDebit: boolean;

  @Column('datetime', { name: 'verified_at', nullable: true })
  verifiedAt: Date | null;

  @Column('varchar', {
    name: 'auth_token',
    nullable: true,
    unique: true,
    length: 255,
  })
  authToken: string | null;

  @Column('datetime', { name: 'bvn_verified_at', nullable: true })
  bvnVerifiedAt: Date | null;

  @Column('datetime', { name: 'work_email_verified_at', nullable: true })
  workEmailVerifiedAt: Date | null;

  @Column('datetime', { name: 'nin_verified_at', nullable: true })
  ninVerifiedAt: Date | null;

  @Column('datetime', { name: 'bvn_bypassed_at', nullable: true })
  bvnBypassedAt: Date | null;

  @Column('varchar', { name: 'loanbot_status', nullable: true, length: 255 })
  loanbotStatus: string | null;


  @Column('varchar', { name: 'loanbot_decision', nullable: true, length: 255 })
  loanbotDecision: string | null;

  @Column('varchar', { name: 'tag', nullable: true, length: 255 })
  tag: string | null;

  @Column('varchar', { name: 'zip_code', nullable: true, length: 255 })
  zipCode: string | null;

  @Column('varchar', {
    name: 'loanbot_identifier',
    nullable: true,
    length: 255,
  })
  loanbotIdentifier: string | null;

  @Column('tinyint', { name: 'is_blacklisted', width: 1, default: () => "'0'" })
  isBlacklisted: boolean;

  @Column('varchar', { name: 'device_id', nullable: true, length: 255 })
  deviceId: string | null;

  @Column('tinyint', { name: 'pnd', width: 1, default: () => "'0'" })
  pnd: boolean;

  @Column('varchar', { name: 'pnd_reason', nullable: true, length: 255 })
  pndReason: string | null;

  @Column('timestamp', { name: 'phone_verified_at', nullable: true })
  phoneVerifiedAt: Date | null;

  @Column('timestamp', { name: 'selfie_verified_at', nullable: true })
  selfieVerifiedAt: Date | null;

  @Column('varchar', { name: 'pnd_channel', nullable: true, length: 100 })
  pndChannel: string | null;

  @Column('varchar', { name: 'two_factor_code', nullable: true, length: 255 })
  twoFactorCode: string | null;

  @Column('datetime', { name: 'two_factor_expires_at', nullable: true })
  twoFactorExpiresAt: Date | null;

  @Column('varchar', { name: 'operating_system', nullable: true, length: 50 })
  operatingSystem: string | null;

  @Column('int', { name: 'bnpl_status', nullable: true, default: () => "'1'" })
  bnplStatus: number | null;

  @Column('tinyint', {
    name: 'caas_onboarding',
    width: 1,
    default: () => "'0'",
  })
  caasOnboarding: boolean;

  @Column('varchar', { name: 'registration_site', nullable: true, length: 255 })
  registrationSite: string | null;

  @Column('varchar', { name: 'user_type', nullable: true, length: 255 })
  userType: string | null;

  @OneToMany(
    () => AdminUserActions,
    (adminUserActions) => adminUserActions.admin,
  )
  adminUserActions: AdminUserActions[];

  @OneToMany(
    () => AdminUserActions,
    (adminUserActions) => adminUserActions.user,
  )
  adminUserActions2: AdminUserActions[];

  @OneToMany(() => AdminUsers, (adminUsers) => adminUsers.admin)
  adminUsers: AdminUsers[];

  @OneToMany(() => AdminUsers, (adminUsers) => adminUsers.user)
  adminUsers2: AdminUsers[];

  @OneToMany(() => AdvanclyLoans, (advanclyLoans) => advanclyLoans.user)
  advanclyLoans: AdvanclyLoans[];

  @OneToMany(() => AppealRequests, (appealRequests) => appealRequests.user)
  appealRequests: AppealRequests[];

  @OneToMany(
    () => ApproverDepartment,
    (approverDepartment) => approverDepartment.approver,
  )
  approverDepartments: ApproverDepartment[];

  @OneToMany(
    () => AssignableAdmins,
    (assignableAdmins) => assignableAdmins.authenticator,
  )
  assignableAdmins: AssignableAdmins[];

  @OneToMany(
    () => AssignableAdmins,
    (assignableAdmins) => assignableAdmins.user,
  )
  assignableAdmins2: AssignableAdmins[];

  @OneToMany(() => AutoLimitJobs, (autoLimitJobs) => autoLimitJobs.user)
  autoLimitJobs: AutoLimitJobs[];

  @OneToMany(() => BankTransfers, (bankTransfers) => bankTransfers.user)
  bankTransfers: BankTransfers[];

  @OneToMany(() => BaxiLogs, (baxiLogs) => baxiLogs.user)
  baxiLogs: BaxiLogs[];

  @OneToMany(
    () => BillTransactions,
    (billTransactions) => billTransactions.user,
  )
  billTransactions: BillTransactions[];

  @OneToMany(
    () => CashWalletTransferAwaitingApprovals,
    (cashWalletTransferAwaitingApprovals) =>
      cashWalletTransferAwaitingApprovals.admin,
  )
  cashWalletTransferAwaitingApprovals: CashWalletTransferAwaitingApprovals[];

  @OneToMany(
    () => CashWalletTransferAwaitingApprovals,
    (cashWalletTransferAwaitingApprovals) =>
      cashWalletTransferAwaitingApprovals.user,
  )
  cashWalletTransferAwaitingApprovals2: CashWalletTransferAwaitingApprovals[];

  @OneToMany(() => Comments, (comments) => comments.commentBy2)
  comments: Comments[];

  @OneToMany(() => Comments, (comments) => comments.user)
  comments2: Comments[];

  @OneToMany(
    () => CommunicationTemplates,
    (communicationTemplates) => communicationTemplates.createdBy2,
  )
  communicationTemplates: CommunicationTemplates[];

  @OneToMany(
    () => CommunicationTemplates,
    (communicationTemplates) => communicationTemplates.updatedBy2,
  )
  communicationTemplates2: CommunicationTemplates[];

  @OneToMany(() => Communications, (communications) => communications.admin)
  communications: Communications[];

  @OneToMany(() => Communications, (communications) => communications.user)
  communications2: Communications[];

  @OneToMany(
    () => CompanyAccounts,
    (companyAccounts) => companyAccounts.manager,
  )
  companyAccounts: CompanyAccounts[];

  @OneToMany(() => CompanyAccounts, (companyAccounts) => companyAccounts.user)
  companyAccounts2: CompanyAccounts[];

  @OneToMany(() => CompanyCards, (companyCards) => companyCards.employee)
  companyCards: CompanyCards[];

  @OneToMany(() => CompanyCards, (companyCards) => companyCards.manager)
  companyCards2: CompanyCards[];

  @OneToMany(
    () => CompanyCustomPlanRequests,
    (companyCustomPlanRequests) => companyCustomPlanRequests.user,
  )
  companyCustomPlanRequests: CompanyCustomPlanRequests[];

  @OneToMany(
    () => CompanyPlanUpgradeRequests,
    (companyPlanUpgradeRequests) => companyPlanUpgradeRequests.user,
  )
  companyPlanUpgradeRequests: CompanyPlanUpgradeRequests[];

  @OneToMany(
    () => CreditCardActivities,
    (creditCardActivities) => creditCardActivities.editor,
  )
  creditCardActivities: CreditCardActivities[];

  @OneToMany(
    () => CreditCardPayments,
    (creditCardPayments) => creditCardPayments.user,
  )
  creditCardPayments: CreditCardPayments[];

  @OneToMany(
    () => CreditCardPendingRepayments,
    (creditCardPendingRepayments) => creditCardPendingRepayments.admin,
  )
  creditCardPendingRepayments: CreditCardPendingRepayments[];

  @OneToMany(
    () => CreditCardPendingRepayments,
    (creditCardPendingRepayments) => creditCardPendingRepayments.user,
  )
  creditCardPendingRepayments2: CreditCardPendingRepayments[];

  @OneToMany(
    () => CreditCardRepaymentReceipts,
    (creditCardRepaymentReceipts) => creditCardRepaymentReceipts.user,
  )
  creditCardRepaymentReceipts: CreditCardRepaymentReceipts[];

  @OneToMany(
    () => CreditCardWalletHistories,
    (creditCardWalletHistories) => creditCardWalletHistories.user,
  )
  creditCardWalletHistories: CreditCardWalletHistories[];

  @OneToMany(
    () => CustomNotifications,
    (customNotifications) => customNotifications.user,
  )
  customNotifications: CustomNotifications[];

  @OneToMany(() => Devices, (devices) => devices.user)
  devices: Devices[];

  @OneToMany(() => Documents, (documents) => documents.user)
  documents: Documents[];

  @OneToMany(
    () => EquityContributions,
    (equityContributions) => equityContributions.user,
  )
  equityContributions: EquityContributions[];

  @OneToMany(
    () => ExpenseApprovers,
    (expenseApprovers) => expenseApprovers.user,
  )
  expenseApprovers: ExpenseApprovers[];

  @OneToMany(
    () => ExpensePolicyUser,
    (expensePolicyUser) => expensePolicyUser.user,
  )
  expensePolicyUsers: ExpensePolicyUser[];

  @OneToMany(() => ExpenseRequests, (expenseRequests) => expenseRequests.user)
  expenseRequests: ExpenseRequests[];

  @OneToMany(
    () => ExpressVerifications,
    (expressVerifications) => expressVerifications.user,
  )
  expressVerifications: ExpressVerifications[];

  @OneToMany(() => FailedPayments, (failedPayments) => failedPayments.user)
  failedPayments: FailedPayments[];

  @OneToMany(
    () => FeatureNotificationSubscriptions,
    (featureNotificationSubscriptions) => featureNotificationSubscriptions.user,
  )
  featureNotificationSubscriptions: FeatureNotificationSubscriptions[];

  @OneToMany(
    () => FireBaseDeviceTokens,
    (fireBaseDeviceTokens) => fireBaseDeviceTokens.user,
  )
  fireBaseDeviceTokens: FireBaseDeviceTokens[];

  @OneToMany(() => FlutterwaveLogs, (flutterwaveLogs) => flutterwaveLogs.user)
  flutterwaveLogs: FlutterwaveLogs[];

  @OneToMany(
    () => IncreaseLoanLimits,
    (increaseLoanLimits) => increaseLoanLimits.user,
  )
  increaseLoanLimits: IncreaseLoanLimits[];

  @OneToMany(
    () => InvalidateDocuments,
    (invalidateDocuments) => invalidateDocuments.user,
  )
  invalidateDocuments: InvalidateDocuments[];

  @OneToMany(
    () => LenderCreditCardLoans,
    (lenderCreditCardLoans) => lenderCreditCardLoans.user,
  )
  lenderCreditCardLoans: LenderCreditCardLoans[];

  @OneToMany(
    () => LoanbotBlankStatements,
    (loanbotBlankStatements) => loanbotBlankStatements.user,
  )
  loanbotBlankStatements: LoanbotBlankStatements[];

  @OneToMany(
    () => LoanbotStatementAnalyses,
    (loanbotStatementAnalyses) => loanbotStatementAnalyses.user,
  )
  loanbotStatementAnalyses: LoanbotStatementAnalyses[];

  @OneToMany(
    () => LoanbotVerifications,
    (loanbotVerifications) => loanbotVerifications.user,
  )
  loanbotVerifications: LoanbotVerifications[];

  @OneToMany(() => Loans, (loans) => loans.user)
  loans: Loans[];

  @OneToMany(
    () => MerchantAnonymousUsers,
    (merchantAnonymousUsers) => merchantAnonymousUsers.user,
  )
  merchantAnonymousUsers: MerchantAnonymousUsers[];

  @OneToMany(() => MerchantOrders, (merchantOrders) => merchantOrders.user)
  merchantOrders: MerchantOrders[];

  @OneToMany(() => MerchantUser, (merchantUser) => merchantUser.user)
  merchantUsers: MerchantUser[];

  @OneToMany(
    () => MyBankStatements,
    (myBankStatements) => myBankStatements.user,
  )
  myBankStatements: MyBankStatements[];

  @OneToMany(
    () => OtpVerifications,
    (otpVerifications) => otpVerifications.user,
  )
  otpVerifications: OtpVerifications[];

  @OneToMany(() => Payments, (payments) => payments.user)
  payments: Payments[];

  @OneToMany(
    () => PaystackTransferRecipients,
    (paystackTransferRecipients) => paystackTransferRecipients.user,
  )
  paystackTransferRecipients: PaystackTransferRecipients[];

  @OneToMany(
    () => PersonalAccountStatements,
    (personalAccountStatements) => personalAccountStatements.user,
  )
  personalAccountStatements: PersonalAccountStatements[];

  @OneToMany(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.user,
  )
  personalCardAccounts: PersonalCardAccounts[];

  @OneToMany(
    () => PersonalCardReconciliations,
    (personalCardReconciliations) => personalCardReconciliations.user,
  )
  personalCardReconciliations: PersonalCardReconciliations[];

  @OneToMany(
    () => PersonalCardTransactions,
    (personalCardTransactions) => personalCardTransactions.user,
  )
  personalCardTransactions: PersonalCardTransactions[];

  @OneToMany(
    () => PersonalCardUtilizations,
    (personalCardUtilizations) => personalCardUtilizations.user,
  )
  personalCardUtilizations: PersonalCardUtilizations[];

  @OneToMany(
    () => PersonalClearedStatements,
    (personalClearedStatements) => personalClearedStatements.user,
  )
  personalClearedStatements: PersonalClearedStatements[];

  @OneToMany(
    () => PersonalCreditApplications,
    (personalCreditApplications) => personalCreditApplications.authorizer,
  )
  personalCreditApplications: PersonalCreditApplications[];

  @OneToMany(
    () => PersonalCreditApplications,
    (personalCreditApplications) => personalCreditApplications.user,
  )
  personalCreditApplications2: PersonalCreditApplications[];

  @OneToMany(
    () => PersonalCreditCardRepayments,
    (personalCreditCardRepayments) => personalCreditCardRepayments.editor,
  )
  personalCreditCardRepayments: PersonalCreditCardRepayments[];

  @OneToMany(
    () => PersonalCreditCardRepayments,
    (personalCreditCardRepayments) => personalCreditCardRepayments.user,
  )
  personalCreditCardRepayments2: PersonalCreditCardRepayments[];

  @OneToMany(
    () => PersonalCredits,
    (personalCredits) => personalCredits.authorizer,
  )
  personalCredits: PersonalCredits[];

  @OneToMany(() => PersonalCredits, (personalCredits) => personalCredits.user)
  personalCredits2: PersonalCredits[];

  @OneToMany(
    () => PersonalDeferredPlanPayments,
    (personalDeferredPlanPayments) => personalDeferredPlanPayments.user,
  )
  personalDeferredPlanPayments: PersonalDeferredPlanPayments[];

  @OneToMany(
    () => PersonalLoanOfferLetters,
    (personalLoanOfferLetters) => personalLoanOfferLetters.user,
  )
  personalLoanOfferLetters: PersonalLoanOfferLetters[];

  @OneToMany(
    () => PersonalLoanRepaymentDefaultCharges,
    (personalLoanRepaymentDefaultCharges) =>
      personalLoanRepaymentDefaultCharges.user,
  )
  personalLoanRepaymentDefaultCharges: PersonalLoanRepaymentDefaultCharges[];

  @OneToMany(
    () => PersonalRepaymentCards,
    (personalRepaymentCards) => personalRepaymentCards.user,
  )
  personalRepaymentCards: PersonalRepaymentCards[];

  @OneToMany(
    () => PersonalRepaymentTransactions,
    (personalRepaymentTransactions) => personalRepaymentTransactions.user,
  )
  personalRepaymentTransactions: PersonalRepaymentTransactions[];

  @OneToMany(() => RemitaLogs, (remitaLogs) => remitaLogs.user)
  remitaLogs: RemitaLogs[];

  @OneToMany(
    () => RepaymentWalletHistories,
    (repaymentWalletHistories) => repaymentWalletHistories.user,
  )
  repaymentWalletHistories: RepaymentWalletHistories[];

  @OneToMany(
    () => RepaymentWalletTransactions,
    (repaymentWalletTransactions) => repaymentWalletTransactions.user,
  )
  repaymentWalletTransactions: RepaymentWalletTransactions[];

  @OneToMany(() => Repayments, (repayments) => repayments.user)
  repayments: Repayments[];

  @OneToMany(() => RequeuedUsers, (requeuedUsers) => requeuedUsers.user)
  requeuedUsers: RequeuedUsers[];

  @OneToMany(() => SecureCards, (secureCards) => secureCards.markAsPaidBy2)
  secureCards: SecureCards[];

  @OneToMany(() => SecureCards, (secureCards) => secureCards.user)
  secureCards2: SecureCards[];

  @OneToMany(
    () => SecuredInvestments,
    (securedInvestments) => securedInvestments.user,
  )
  securedInvestments: SecuredInvestments[];

  @OneToMany(
    () => StatementDocuments,
    (statementDocuments) => statementDocuments.user,
  )
  statementDocuments: StatementDocuments[];

  @OneToMany(() => TangarineTasks, (tangarineTasks) => tangarineTasks.user)
  tangarineTasks: TangarineTasks[];

  @OneToMany(() => TransactionPins, (transactionPins) => transactionPins.user)
  transactionPins: TransactionPins[];

  @OneToMany(
    () => TransferCollections,
    (transferCollections) => transferCollections.user,
  )
  transferCollections: TransferCollections[];

  @OneToMany(() => Transfers, (transfers) => transfers.user)
  transfers: Transfers[];

  @OneToMany(() => Trips, (trips) => trips.user)
  trips: Trips[];

  @OneToMany(
    () => UsedTransactionPins,
    (usedTransactionPins) => usedTransactionPins.user,
  )
  usedTransactionPins: UsedTransactionPins[];

  @OneToMany(
    () => UserBankStatements,
    (userBankStatements) => userBankStatements.user,
  )
  userBankStatements: UserBankStatements[];

  @OneToMany(() => UserPlans, (userPlans) => userPlans.user)
  userPlans: UserPlans[];

  @OneToMany(() => UserProfiles, (userProfiles) => userProfiles.user)
  userProfiles: UserProfiles[];

  @OneToMany(() => UserReferrals, (userReferrals) => userReferrals.referred)
  userReferrals: UserReferrals[];

  @OneToMany(() => UserReferrals, (userReferrals) => userReferrals.user)
  userReferrals2: UserReferrals[];

  @OneToMany(
    () => UserSecurityQuestions,
    (userSecurityQuestions) => userSecurityQuestions.user,
  )
  userSecurityQuestions: UserSecurityQuestions[];

  @OneToMany(
    () => UserStatementComments,
    (userStatementComments) => userStatementComments.admin,
  )
  userStatementComments: UserStatementComments[];

  @OneToMany(
    () => UserStatementComments,
    (userStatementComments) => userStatementComments.user,
  )
  userStatementComments2: UserStatementComments[];

  @ManyToOne(() => Companies, (companies) => companies.users, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'company_id', referencedColumnName: 'id' }])
  company: Companies;

  @ManyToOne(() => Groups, (groups) => groups.users, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'group_id', referencedColumnName: 'id' }])
  group: Groups;

  @ManyToOne(() => Merchants, (merchants) => merchants.users, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'merchant_id', referencedColumnName: 'id' }])
  merchant: Merchants;

  @ManyToOne(() => Units, (units) => units.users, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'unit_id', referencedColumnName: 'id' }])
  unit: Units;

  @ManyToMany(() => Permissions, (permissions) => permissions.users)
  permissions: Permissions[];

  @ManyToMany(() => Roles, (roles) => roles.users)
  roles: Roles[];

  @OneToMany(
    () => VerifyDeclinedPayments,
    (verifyDeclinedPayments) => verifyDeclinedPayments.user,
  )
  verifyDeclinedPayments: VerifyDeclinedPayments[];

  @OneToMany(() => VirtualAccounts, (virtualAccounts) => virtualAccounts.user)
  virtualAccounts: VirtualAccounts[];

  @OneToMany(
    () => WalletLimitRequests,
    (walletLimitRequests) => walletLimitRequests.user,
  )
  walletLimitRequests: WalletLimitRequests[];

  @OneToMany(() => Watchlists, (watchlists) => watchlists.user)
  watchlists: Watchlists[];

  @OneToMany(
    () => WorkplaceVerifications,
    (workplaceVerifications) => workplaceVerifications.user,
  )
  workplaceVerifications: WorkplaceVerifications[];

  @OneToMany(() => SelfieAttempt, (attempt) => attempt.user)
  selfieAttempts: SelfieAttempt[];
}
