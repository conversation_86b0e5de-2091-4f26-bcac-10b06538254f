import { BadRequestException, Injectable } from '@nestjs/common';
import { RedisService } from '@crednet/utils';
import { UserRepository } from 'src/user/repository/user.repository';
import { UserProfileRepository } from 'src/user/repository/user-profile.repository';
import { AuthData } from '@crednet/authmanager';
import { SmsService } from 'src/integration/sms/sms.service';
import { WhatsAppService } from 'src/integration/whatsapp/whatsapp.service';
import { VerifyOtpDtoOtpDto } from './dto/verify-otp.dto';
import * as crypto from 'crypto';
import { VerificationService } from '@app/verification';
import config from 'src/config';
import { DeviceRepository } from './repository/device.repository';
import { PinRepository } from './repository/pin.repository';
import { UsedPinRepository } from './repository/used-pin.repository';
import * as bcrypt from 'bcrypt';
import { VerificationMethod } from './entities/kyc.entity';
import { TelegramService } from 'src/integration/telegram/telegram.service';
import { SelfieAttemptRepository } from './repository/selfie-attempt.repository';

@Injectable()
export class KycService {
  constructor(
    private readonly redis: RedisService,
    private readonly userRepository: UserRepository,
    private readonly whatsappService: WhatsAppService,
    private readonly smsService: SmsService,
    private readonly verificationService: VerificationService,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly redisService: RedisService,
    private readonly deviceRepository: DeviceRepository,
    private readonly pinRepository: PinRepository,
    private readonly usedPinRepository: UsedPinRepository,
    private readonly telegramService: TelegramService,
    private readonly selfieAttemptRepository: SelfieAttemptRepository,
  ) {}

  async sendOtp(
    auth: AuthData,
    phoneNumber: string,
    verificationMethod: VerificationMethod,
  ) {
    // Generate a cryptographically secure 6-digit OTP
    let otp = await this.redis.get(`otp:${auth.id}`);
    if (!otp || otp.length != 6) {
      const otpBuffer = crypto.randomBytes(3);
      otp = ((otpBuffer.readUIntBE(0, 3) % 900000) + 100000).toString();
      await this.redis.del(`otp:${auth.id}`);
    }

    await this.redis.set(`otp:${auth.id}`, otp, { PX: 60 * 100 * 1000 });

    const message = `Hi, Your CredPal authentication code is ${otp}. This code expires after 10 minutes`;

    await this.sendNotificaations(
      phoneNumber,
      message,
      otp.toString(),
      verificationMethod,
    );
    return otp;
  }

  async verifyOtp(auth: AuthData, dto: VerifyOtpDtoOtpDto) {
    const otpFromRedis = await this.redis.get(`otp:${auth.id}`);
    if (otpFromRedis !== String(dto.otp)) {
      throw new BadRequestException('Invalid OTP');
    }
    await this.userRepository.update(
      { id: auth.id + '' },
      { phoneVerifiedAt: new Date(), mobileVerifiedAt: new Date() },
    );

    await this.redis.del(`otp:${auth.id}`);
    return `account verified`;
  }

  async verifyFace(file: Express.Multer.File, userId: string) {
    const profile = await this.userProfileRepository.findOne({
      where: { userId },
      // select: { bvn: true },
    });
    const user = await this.userRepository.findOne({
      where: { id: userId },
      // select: { bvn: true },
    });

    if (!profile || !user) {
      throw new BadRequestException('Profile not found');
    }
    const face = await this.verificationService.verifyFace(profile.bvn, file);



    if (face.confidence < 70) {
      throw new BadRequestException('Face not verified, please try again');
    }

    const update = {};

    if (!user.selfieVerifiedAt) {
      update['selfieVerifiedAt'] = new Date();
    }

    if (!user.bvnVerifiedAt) {
      update['bvnVerifiedAt'] = new Date();
    }

    if (Object.keys(update).length > 0) {
      await this.userRepository.update({ id: userId }, update);
    }
    this.saveSelfieAttempt(userId, profile.bvn, file, face);
    return `account verified`;
  }

  async saveSelfieAttempt(
    userId: string,
    bvn: string,
    file: Express.Multer.File,
    metadata: object,
  ) {
    let image;
    try {
      image = await this.verificationService.uploadImage(file);
    } catch (error) {
      console.log(error);
    }
    await this.selfieAttemptRepository.insert({
      userId: parseInt(userId),
      bvn,
      image,
      response: metadata,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  async resetPin(file: Express.Multer.File, userId: string, pin: string) {
    await this.verifyFace(file, userId);

    const transactionPin = await this.pinRepository.findOne({
      where: { userId },
    });

    if (pin) {
      await this.usedPinRepository.save({
        pin: transactionPin.pin,
        createdAt: new Date(),
        updatedAt: new Date(),
        userId,
        isUsed: true,
        usedAt: new Date(),
        usedBy: userId,
      });
    }

    let hashedPin = await bcrypt.hash(pin, 10);
    hashedPin = hashedPin.replace(/^\$2a(.+)$/i, '$2y$1');

    transactionPin.pin = hashedPin;
    transactionPin.updatedAt = new Date();
    await this.pinRepository.save(transactionPin);

    return { message: `Pin reset successfully` };
  }

  async initiateKyc(auth: AuthData) {
    const user = await this.userRepository.findOne({
      where: { id: auth.id + '' },
    });
  }

  async sendNotificaations(
    phoneNumber: string,
    message: string,
    otp: string,
    verificationMethod: VerificationMethod,
  ) {
    try {
      
    switch (verificationMethod) {
      case VerificationMethod.WHATSAPP:
        await this.whatsappService.sendMessage(phoneNumber, otp);
        break;
      case VerificationMethod.TELEGRAM:
        await this.telegramService.sendMessage(phoneNumber, otp);
        break;

      case VerificationMethod.SMS:
      default:
        this.smsService.sendSms(phoneNumber, message);
    }

  } catch (error) {
    console.log(error);
    throw new BadRequestException('Failed to send OTP, please try another method');
      
  }
  }

  async resetDevice(token: string, file: Express.Multer.File) {
    const userId = await this.getUserIdFromResetToken(token);
    if (!userId) {
      throw new BadRequestException('Invalid token');
    }
    await this.verifyFace(file, userId);

    await this.redisService.del(
      `${config.crednetCachePrefix}user_reset_token:${token}`,
    );
    await this.deviceRepository.update({ userId }, { isDeviceTrusted: false });
    return `Device reset successfully`;
  }

  async getUserIdFromResetToken(token: string): Promise<string | null> {
    const redisKey = `${config.crednetCachePrefix}user_reset_token:${token}`;

    try {
      const userId = await this.redisService.get(redisKey);
      console.log(redisKey, userId);

      if (userId === null) {
        console.log(`Token ${token} not found or has expired.`);
        return null; // Token not found or expired
      } else {
        console.log(`Found user ID ${userId} for token ${token}`);
        return userId; // Returns the user ID stored by PHP
      }
    } catch (error) {
      console.error('Error retrieving from Redis:', error);
      throw error; // Handle the error appropriately
    }
  }
}
